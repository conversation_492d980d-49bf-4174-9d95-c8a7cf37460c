package com.eddars.rockpaperscissors

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import com.eddars.rps_gesture.RpsGesturePlugin

class MainActivity : FlutterActivity() {

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Enregistrer manuellement le plugin RpsGesturePlugin
        flutterEngine.plugins.add(RpsGesturePlugin())
    }
}
